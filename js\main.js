// SmartBiz Admin Dashboard - Main JavaScript File

class AdminDashboard {
    constructor() {
        this.currentPage = 'dashboard';
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.sidebarCollapsed = false;
        this.usersData = [];
        this.filteredUsers = [];
        this.currentUserPage = 1;
        this.usersPerPage = 10;
        this.sortColumn = '';
        this.sortDirection = 'asc';
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.applyTheme();
        this.generateUsersData();
        this.initializeCharts();
        this.showPage('dashboard');
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.dataset.page;
                this.showPage(page);
            });
        });

        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const overlay = document.getElementById('overlay');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }

        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => this.toggleMobileSidebar());
        }

        if (overlay) {
            overlay.addEventListener('click', () => this.closeMobileSidebar());
        }

        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Users page functionality
        this.setupUsersPageListeners();
        
        // Reports page functionality
        this.setupReportsPageListeners();
        
        // Settings page functionality
        this.setupSettingsPageListeners();

        // Global search
        const globalSearch = document.getElementById('globalSearch');
        if (globalSearch) {
            globalSearch.addEventListener('input', (e) => this.handleGlobalSearch(e.target.value));
        }

        // Window resize handler
        window.addEventListener('resize', () => this.handleResize());
    }

    showPage(pageName) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
        document.querySelector(`[data-page="${pageName}"]`).parentElement.classList.add('active');

        // Update page content
        document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
        document.getElementById(`${pageName}Page`).classList.add('active');

        // Update page title
        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle) {
            pageTitle.textContent = this.getPageTitle(pageName);
        }

        this.currentPage = pageName;

        // Page-specific initialization
        if (pageName === 'users') {
            this.renderUsersTable();
        } else if (pageName === 'reports') {
            this.updateReportsChart('daily');
        }
    }

    getPageTitle(pageName) {
        const titles = {
            dashboard: 'Dashboard',
            users: 'Users Management',
            reports: 'Reports & Analytics',
            settings: 'Settings'
        };
        return titles[pageName] || 'Dashboard';
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('collapsed');
        this.sidebarCollapsed = !this.sidebarCollapsed;
    }

    toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        
        sidebar.classList.toggle('open');
        overlay.classList.toggle('active');
    }

    closeMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        
        sidebar.classList.remove('open');
        overlay.classList.remove('active');
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        localStorage.setItem('theme', this.currentTheme);
    }

    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            icon.className = this.currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    handleResize() {
        if (window.innerWidth > 768) {
            this.closeMobileSidebar();
        }
    }

    handleGlobalSearch(query) {
        // Implement global search functionality
        console.log('Global search:', query);
        
        if (this.currentPage === 'users') {
            this.filterUsers();
        }
    }

    // Generate sample users data
    generateUsersData() {
        const names = ['John Doe', 'Jane Smith', 'Mike Johnson', 'Sarah Wilson', 'David Brown', 'Lisa Davis', 'Tom Miller', 'Anna Garcia', 'Chris Martinez', 'Emma Taylor', 'Ryan Anderson', 'Olivia Thomas', 'James Jackson', 'Sophia White', 'Daniel Harris', 'Isabella Martin', 'Matthew Thompson', 'Mia Garcia', 'Anthony Rodriguez', 'Charlotte Lewis'];
        const roles = ['admin', 'user', 'moderator'];
        const statuses = ['active', 'inactive', 'pending'];
        
        this.usersData = [];
        
        for (let i = 0; i < 50; i++) {
            const name = names[Math.floor(Math.random() * names.length)];
            const email = `${name.toLowerCase().replace(' ', '.')}@example.com`;
            const role = roles[Math.floor(Math.random() * roles.length)];
            const status = statuses[Math.floor(Math.random() * statuses.length)];
            const joinDate = new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28));
            
            this.usersData.push({
                id: i + 1,
                name,
                email,
                role,
                status,
                joinDate: joinDate.toLocaleDateString()
            });
        }
        
        this.filteredUsers = [...this.usersData];
    }

    setupUsersPageListeners() {
        // Search functionality
        const userSearch = document.getElementById('userSearch');
        if (userSearch) {
            userSearch.addEventListener('input', () => this.filterUsers());
        }

        // Filter dropdowns
        const roleFilter = document.getElementById('roleFilter');
        const statusFilter = document.getElementById('statusFilter');
        
        if (roleFilter) {
            roleFilter.addEventListener('change', () => this.filterUsers());
        }
        
        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.filterUsers());
        }

        // Table sorting
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => {
                const sortBy = header.dataset.sort;
                this.sortUsers(sortBy);
            });
        });
    }

    filterUsers() {
        const searchTerm = document.getElementById('userSearch')?.value.toLowerCase() || '';
        const roleFilter = document.getElementById('roleFilter')?.value || '';
        const statusFilter = document.getElementById('statusFilter')?.value || '';
        const globalSearch = document.getElementById('globalSearch')?.value.toLowerCase() || '';

        this.filteredUsers = this.usersData.filter(user => {
            const matchesSearch = user.name.toLowerCase().includes(searchTerm) || 
                                user.email.toLowerCase().includes(searchTerm);
            const matchesRole = !roleFilter || user.role === roleFilter;
            const matchesStatus = !statusFilter || user.status === statusFilter;
            const matchesGlobal = !globalSearch || 
                                user.name.toLowerCase().includes(globalSearch) || 
                                user.email.toLowerCase().includes(globalSearch);

            return matchesSearch && matchesRole && matchesStatus && matchesGlobal;
        });

        this.currentUserPage = 1;
        this.renderUsersTable();
    }

    sortUsers(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }

        this.filteredUsers.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];

            if (column === 'joinDate') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            }

            if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
            return 0;
        });

        // Update sort indicators
        document.querySelectorAll('.sortable').forEach(header => {
            header.classList.remove('sort-asc', 'sort-desc');
        });

        const currentHeader = document.querySelector(`[data-sort="${column}"]`);
        if (currentHeader) {
            currentHeader.classList.add(`sort-${this.sortDirection}`);
        }

        this.renderUsersTable();
    }

    renderUsersTable() {
        const tbody = document.getElementById('usersTableBody');
        if (!tbody) return;

        const startIndex = (this.currentUserPage - 1) * this.usersPerPage;
        const endIndex = startIndex + this.usersPerPage;
        const pageUsers = this.filteredUsers.slice(startIndex, endIndex);

        tbody.innerHTML = pageUsers.map(user => `
            <tr>
                <td>${user.name}</td>
                <td>${user.email}</td>
                <td><span class="role-badge ${user.role}">${user.role}</span></td>
                <td><span class="status-badge ${user.status}">${user.status}</span></td>
                <td>${user.joinDate}</td>
                <td>
                    <button class="action-btn edit" title="Edit User">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" title="Delete User">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');

        this.renderPagination();
    }

    renderPagination() {
        const pagination = document.getElementById('usersPagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.filteredUsers.length / this.usersPerPage);
        const startItem = (this.currentUserPage - 1) * this.usersPerPage + 1;
        const endItem = Math.min(this.currentUserPage * this.usersPerPage, this.filteredUsers.length);

        pagination.innerHTML = `
            <div class="pagination-info">
                Showing ${startItem} to ${endItem} of ${this.filteredUsers.length} entries
            </div>
            <div class="pagination-controls">
                <button class="pagination-btn" ${this.currentUserPage === 1 ? 'disabled' : ''} onclick="dashboard.changePage(${this.currentUserPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </button>
                ${this.generatePageNumbers(totalPages)}
                <button class="pagination-btn" ${this.currentUserPage === totalPages ? 'disabled' : ''} onclick="dashboard.changePage(${this.currentUserPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        `;
    }

    generatePageNumbers(totalPages) {
        let pages = '';
        const maxVisible = 5;
        let startPage = Math.max(1, this.currentUserPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(totalPages, startPage + maxVisible - 1);

        if (endPage - startPage + 1 < maxVisible) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            pages += `
                <button class="pagination-btn ${i === this.currentUserPage ? 'active' : ''}" 
                        onclick="dashboard.changePage(${i})">
                    ${i}
                </button>
            `;
        }

        return pages;
    }

    changePage(page) {
        const totalPages = Math.ceil(this.filteredUsers.length / this.usersPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentUserPage = page;
            this.renderUsersTable();
        }
    }

    // Charts initialization
    initializeCharts() {
        this.initDashboardCharts();
        this.initReportsChart();
    }

    initDashboardCharts() {
        // Revenue Chart (Line Chart)
        const revenueCtx = document.getElementById('revenueChart');
        if (revenueCtx) {
            this.charts.revenue = new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    datasets: [{
                        label: 'Monthly Revenue',
                        data: [12000, 19000, 15000, 25000, 22000, 30000, 28000, 35000, 32000, 40000, 38000, 45000],
                        borderColor: '#4f46e5',
                        backgroundColor: 'rgba(79, 70, 229, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#4f46e5',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // User Distribution Chart (Pie Chart)
        const userCtx = document.getElementById('userChart');
        if (userCtx) {
            this.charts.userDistribution = new Chart(userCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Admin', 'Users', 'Moderators'],
                    datasets: [{
                        data: [15, 65, 20],
                        backgroundColor: [
                            '#ef4444',
                            '#4f46e5',
                            '#f59e0b'
                        ],
                        borderWidth: 0,
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        }
    }

    initReportsChart() {
        const visitsCtx = document.getElementById('visitsChart');
        if (visitsCtx) {
            this.charts.visits = new Chart(visitsCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Visits',
                        data: [],
                        backgroundColor: 'rgba(79, 70, 229, 0.8)',
                        borderColor: '#4f46e5',
                        borderWidth: 1,
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }
    }

    setupReportsPageListeners() {
        document.querySelectorAll('.toggle-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.target.dataset.view;
                if (view) {
                    // Update active button
                    document.querySelectorAll('.toggle-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');

                    // Update chart
                    this.updateReportsChart(view);
                }
            });
        });
    }

    updateReportsChart(view) {
        if (!this.charts.visits) return;

        let labels, data;

        switch (view) {
            case 'daily':
                labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
                data = [1200, 1900, 1500, 2200, 1800, 2400, 2100];
                break;
            case 'weekly':
                labels = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
                data = [8500, 9200, 8800, 9600];
                break;
            case 'monthly':
                labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
                data = [35000, 42000, 38000, 45000, 41000, 48000];
                break;
            default:
                return;
        }

        this.charts.visits.data.labels = labels;
        this.charts.visits.data.datasets[0].data = data;
        this.charts.visits.update();
    }

    setupSettingsPageListeners() {
        // Toggle switches
        document.querySelectorAll('.toggle-switch input').forEach(toggle => {
            toggle.addEventListener('change', (e) => {
                console.log(`Setting ${e.target.id} changed to:`, e.target.checked);
                // Here you would typically save the setting
            });
        });

        // Select dropdowns
        document.querySelectorAll('.setting-select').forEach(select => {
            select.addEventListener('change', (e) => {
                console.log(`Setting ${e.target.name || 'unnamed'} changed to:`, e.target.value);
                // Here you would typically save the setting
            });
        });

        // Save button
        const saveBtn = document.querySelector('.settings-actions .btn-primary');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }

        // Reset button
        const resetBtn = document.querySelector('.settings-actions .btn-secondary');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetSettings();
            });
        }
    }

    saveSettings() {
        // Collect all settings
        const settings = {};

        // Toggle switches
        document.querySelectorAll('.toggle-switch input').forEach(toggle => {
            settings[toggle.id] = toggle.checked;
        });

        // Select dropdowns
        document.querySelectorAll('.setting-select').forEach(select => {
            settings[select.name || 'unnamed'] = select.value;
        });

        // Save to localStorage (in a real app, you'd send to server)
        localStorage.setItem('dashboardSettings', JSON.stringify(settings));

        // Show success message (you could implement a toast notification)
        console.log('Settings saved:', settings);
        alert('Settings saved successfully!');
    }

    resetSettings() {
        if (confirm('Are you sure you want to reset all settings to defaults?')) {
            // Reset toggle switches
            document.getElementById('notificationsToggle').checked = true;
            document.getElementById('autosaveToggle').checked = false;
            document.getElementById('publicProfileToggle').checked = false;
            document.getElementById('dataCollectionToggle').checked = true;

            // Reset select dropdowns to first option
            document.querySelectorAll('.setting-select').forEach(select => {
                select.selectedIndex = 0;
            });

            // Clear saved settings
            localStorage.removeItem('dashboardSettings');

            console.log('Settings reset to defaults');
            alert('Settings reset to defaults!');
        }
    }
}

// Initialize the dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new AdminDashboard();
});
