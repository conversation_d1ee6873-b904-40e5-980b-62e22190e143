// SmartBiz Admin Dashboard - Main JavaScript File

class AdminDashboard {
    constructor() {
        this.currentPage = 'dashboard';
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.sidebarCollapsed = false;
        this.usersData = [];
        this.filteredUsers = [];
        this.currentUserPage = 1;
        this.usersPerPage = 10;
        this.sortColumn = '';
        this.sortDirection = 'asc';
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.applyTheme();
        this.generateUsersData();
        this.initializeCharts();
        this.showPage('dashboard');
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.dataset.page;
                this.showPage(page);
            });
        });

        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const overlay = document.getElementById('overlay');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }

        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => this.toggleMobileSidebar());
        }

        if (overlay) {
            overlay.addEventListener('click', () => this.closeMobileSidebar());
        }

        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Users page functionality
        this.setupUsersPageListeners();
        
        // Reports page functionality
        this.setupReportsPageListeners();
        
        // Settings page functionality
        this.setupSettingsPageListeners();

        // Global search
        const globalSearch = document.getElementById('globalSearch');
        if (globalSearch) {
            globalSearch.addEventListener('input', (e) => this.handleGlobalSearch(e.target.value));
        }

        // Window resize handler
        window.addEventListener('resize', () => this.handleResize());
    }

    showPage(pageName) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
        document.querySelector(`[data-page="${pageName}"]`).parentElement.classList.add('active');

        // Update page content
        document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
        document.getElementById(`${pageName}Page`).classList.add('active');

        // Update page title
        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle) {
            pageTitle.textContent = this.getPageTitle(pageName);
        }

        this.currentPage = pageName;

        // Page-specific initialization
        if (pageName === 'users') {
            this.renderUsersTable();
        } else if (pageName === 'reports') {
            this.updateReportsChart('daily');
        }
    }

    getPageTitle(pageName) {
        const titles = {
            dashboard: 'Dashboard',
            users: 'Users Management',
            reports: 'Reports & Analytics',
            settings: 'Settings'
        };
        return titles[pageName] || 'Dashboard';
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('collapsed');
        this.sidebarCollapsed = !this.sidebarCollapsed;
    }

    toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        
        sidebar.classList.toggle('open');
        overlay.classList.toggle('active');
    }

    closeMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        
        sidebar.classList.remove('open');
        overlay.classList.remove('active');
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        localStorage.setItem('theme', this.currentTheme);
    }

    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            icon.className = this.currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    handleResize() {
        if (window.innerWidth > 768) {
            this.closeMobileSidebar();
        }
    }

    handleGlobalSearch(query) {
        // Implement global search functionality
        console.log('Global search:', query);
        
        if (this.currentPage === 'users') {
            this.filterUsers();
        }
    }

    // Generate sample users data
    generateUsersData() {
        const names = ['John Doe', 'Jane Smith', 'Mike Johnson', 'Sarah Wilson', 'David Brown', 'Lisa Davis', 'Tom Miller', 'Anna Garcia', 'Chris Martinez', 'Emma Taylor', 'Ryan Anderson', 'Olivia Thomas', 'James Jackson', 'Sophia White', 'Daniel Harris', 'Isabella Martin', 'Matthew Thompson', 'Mia Garcia', 'Anthony Rodriguez', 'Charlotte Lewis'];
        const roles = ['admin', 'user', 'moderator'];
        const statuses = ['active', 'inactive', 'pending'];
        
        this.usersData = [];
        
        for (let i = 0; i < 50; i++) {
            const name = names[Math.floor(Math.random() * names.length)];
            const email = `${name.toLowerCase().replace(' ', '.')}@example.com`;
            const role = roles[Math.floor(Math.random() * roles.length)];
            const status = statuses[Math.floor(Math.random() * statuses.length)];
            const joinDate = new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28));
            
            this.usersData.push({
                id: i + 1,
                name,
                email,
                role,
                status,
                joinDate: joinDate.toLocaleDateString()
            });
        }
        
        this.filteredUsers = [...this.usersData];
    }

    setupUsersPageListeners() {
        // Search functionality
        const userSearch = document.getElementById('userSearch');
        if (userSearch) {
            userSearch.addEventListener('input', () => this.filterUsers());
        }

        // Filter dropdowns
        const roleFilter = document.getElementById('roleFilter');
        const statusFilter = document.getElementById('statusFilter');
        
        if (roleFilter) {
            roleFilter.addEventListener('change', () => this.filterUsers());
        }
        
        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.filterUsers());
        }

        // Table sorting
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => {
                const sortBy = header.dataset.sort;
                this.sortUsers(sortBy);
            });
        });
    }

    filterUsers() {
        const searchTerm = document.getElementById('userSearch')?.value.toLowerCase() || '';
        const roleFilter = document.getElementById('roleFilter')?.value || '';
        const statusFilter = document.getElementById('statusFilter')?.value || '';
        const globalSearch = document.getElementById('globalSearch')?.value.toLowerCase() || '';

        this.filteredUsers = this.usersData.filter(user => {
            const matchesSearch = user.name.toLowerCase().includes(searchTerm) || 
                                user.email.toLowerCase().includes(searchTerm);
            const matchesRole = !roleFilter || user.role === roleFilter;
            const matchesStatus = !statusFilter || user.status === statusFilter;
            const matchesGlobal = !globalSearch || 
                                user.name.toLowerCase().includes(globalSearch) || 
                                user.email.toLowerCase().includes(globalSearch);

            return matchesSearch && matchesRole && matchesStatus && matchesGlobal;
        });

        this.currentUserPage = 1;
        this.renderUsersTable();
    }

    sortUsers(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }

        this.filteredUsers.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];

            if (column === 'joinDate') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            }

            if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
            return 0;
        });

        // Update sort indicators
        document.querySelectorAll('.sortable').forEach(header => {
            header.classList.remove('sort-asc', 'sort-desc');
        });

        const currentHeader = document.querySelector(`[data-sort="${column}"]`);
        if (currentHeader) {
            currentHeader.classList.add(`sort-${this.sortDirection}`);
        }

        this.renderUsersTable();
    }

    renderUsersTable() {
        const tbody = document.getElementById('usersTableBody');
        if (!tbody) return;

        const startIndex = (this.currentUserPage - 1) * this.usersPerPage;
        const endIndex = startIndex + this.usersPerPage;
        const pageUsers = this.filteredUsers.slice(startIndex, endIndex);

        tbody.innerHTML = pageUsers.map(user => `
            <tr>
                <td>${user.name}</td>
                <td>${user.email}</td>
                <td><span class="role-badge ${user.role}">${user.role}</span></td>
                <td><span class="status-badge ${user.status}">${user.status}</span></td>
                <td>${user.joinDate}</td>
                <td>
                    <button class="action-btn edit" title="Edit User">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" title="Delete User">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');

        this.renderPagination();
    }

    renderPagination() {
        const pagination = document.getElementById('usersPagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.filteredUsers.length / this.usersPerPage);
        const startItem = (this.currentUserPage - 1) * this.usersPerPage + 1;
        const endItem = Math.min(this.currentUserPage * this.usersPerPage, this.filteredUsers.length);

        pagination.innerHTML = `
            <div class="pagination-info">
                Showing ${startItem} to ${endItem} of ${this.filteredUsers.length} entries
            </div>
            <div class="pagination-controls">
                <button class="pagination-btn" ${this.currentUserPage === 1 ? 'disabled' : ''} onclick="dashboard.changePage(${this.currentUserPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </button>
                ${this.generatePageNumbers(totalPages)}
                <button class="pagination-btn" ${this.currentUserPage === totalPages ? 'disabled' : ''} onclick="dashboard.changePage(${this.currentUserPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        `;
    }

    generatePageNumbers(totalPages) {
        let pages = '';
        const maxVisible = 5;
        let startPage = Math.max(1, this.currentUserPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(totalPages, startPage + maxVisible - 1);

        if (endPage - startPage + 1 < maxVisible) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            pages += `
                <button class="pagination-btn ${i === this.currentUserPage ? 'active' : ''}" 
                        onclick="dashboard.changePage(${i})">
                    ${i}
                </button>
            `;
        }

        return pages;
    }

    changePage(page) {
        const totalPages = Math.ceil(this.filteredUsers.length / this.usersPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentUserPage = page;
            this.renderUsersTable();
        }
    }
}
