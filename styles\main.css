/* CSS Custom Properties for Theme Management */
:root {
    /* Light Theme Colors */
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-sidebar: #1e293b;
    
    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-white: #ffffff;
    
    /* Border Colors */
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    
    /* Shadow */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* Spacing */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --topbar-height: 70px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-sidebar: #020617;
    
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    
    --border-color: #334155;
    --border-hover: #475569;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* App Container */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--bg-sidebar);
    color: var(--text-white);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transition: transform var(--transition-normal), width var(--transition-normal);
    overflow-y: auto;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 1.25rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 700;
}

.logo i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-white);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color var(--transition-fast);
}

.sidebar-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.25rem;
    color: var(--text-white);
    text-decoration: none;
    transition: all var(--transition-fast);
    border-radius: 0;
    position: relative;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-item.active .nav-link {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.nav-link i {
    font-size: 1.125rem;
    width: 1.25rem;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left var(--transition-normal);
}

.sidebar.collapsed + .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* Topbar */
.topbar {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: 0 2rem;
    height: var(--topbar-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    color: var(--text-primary);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.topbar-center {
    flex: 1;
    max-width: 400px;
    margin: 0 2rem;
}

.search-container {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 0.875rem;
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle,
.notification-btn {
    background: none;
    border: none;
    padding: 0.75rem;
    border-radius: 0.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    position: relative;
}

.theme-toggle:hover,
.notification-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.notification-badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    background-color: var(--danger-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 9999px;
    min-width: 1.25rem;
    text-align: center;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.user-profile:hover {
    background-color: var(--bg-tertiary);
}

.user-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    object-fit: cover;
}

.user-name {
    font-weight: 500;
    color: var(--text-primary);
}

/* Page Content */
.page-content {
    padding: 2rem;
    min-height: calc(100vh - var(--topbar-height));
}

.page {
    display: none;
}

.page.active {
    display: block;
}

/* Dashboard Styles */
.dashboard-grid {
    display: grid;
    gap: 2rem;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.summary-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--text-white);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
}

.card-content {
    flex: 1;
}

.card-title {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.card-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.card-change {
    font-size: 0.875rem;
    font-weight: 500;
}

.card-change.positive {
    color: var(--success-color);
}

.card-change.negative {
    color: var(--danger-color);
}

/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.chart-container {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    height: 400px;
    position: relative;
}

.chart-container canvas {
    max-height: 320px !important;
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .summary-cards {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .topbar {
        padding: 0 1rem;
    }
    
    .topbar-center {
        margin: 0 1rem;
        max-width: none;
    }
    
    .page-content {
        padding: 1rem;
    }
    
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .user-name {
        display: none;
    }
}

/* Overlay for mobile */
.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

.overlay.active {
    display: block;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--border-hover);
}

/* Page Header */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.page-header h2 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
}

.page-actions {
    display: flex;
    gap: 1rem;
}

/* Table Styles */
.table-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.filter-input,
.filter-select {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.filter-input {
    min-width: 250px;
}

.filter-select {
    min-width: 150px;
}

.table-container {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background-color: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    position: relative;
}

.data-table th.sortable {
    cursor: pointer;
    user-select: none;
}

.data-table th.sortable:hover {
    background-color: var(--bg-tertiary);
}

.data-table th i {
    margin-left: 0.5rem;
    color: var(--text-muted);
    transition: color var(--transition-fast);
}

.data-table th.sort-asc i::before {
    content: "\f0de";
    color: var(--primary-color);
}

.data-table th.sort-desc i::before {
    content: "\f0dd";
    color: var(--primary-color);
}

.data-table td {
    color: var(--text-secondary);
}

.data-table tbody tr:hover {
    background-color: var(--bg-secondary);
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.active {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.inactive {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--secondary-color);
}

.status-badge.pending {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

/* Role Badges */
.role-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.role-badge.admin {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.role-badge.user {
    background-color: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
}

.role-badge.moderator {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

/* Action Buttons */
.action-btn {
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    color: var(--text-muted);
    transition: all var(--transition-fast);
}

.action-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.action-btn.edit:hover {
    color: var(--primary-color);
}

.action-btn.delete:hover {
    color: var(--danger-color);
}

/* Pagination */
.pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 1.5rem;
    padding: 1rem 0;
}

.pagination-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--bg-tertiary);
    border-color: var(--border-hover);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background-color: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

/* Reports Page */
.report-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.view-toggle {
    display: flex;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    overflow: hidden;
}

.toggle-btn {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all var(--transition-fast);
}

.toggle-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.toggle-btn.active {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.reports-grid {
    display: grid;
    gap: 2rem;
}

.report-chart {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
}

/* Settings Page */
.settings-grid {
    display: grid;
    gap: 2rem;
    max-width: 800px;
}

.settings-section {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
}

.settings-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
}

.setting-select {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    min-width: 150px;
}

.setting-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 3rem;
    height: 1.5rem;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--secondary-color);
    transition: var(--transition-normal);
    border-radius: 1.5rem;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 1.125rem;
    width: 1.125rem;
    left: 0.1875rem;
    bottom: 0.1875rem;
    background-color: white;
    transition: var(--transition-normal);
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(1.5rem);
}

.toggle-slider:hover {
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Settings Actions */
.settings-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

/* Mobile Responsive Adjustments */
@media (max-width: 640px) {
    .table-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-input,
    .filter-select {
        min-width: auto;
        width: 100%;
    }

    .table-container {
        overflow-x: auto;
    }

    .data-table {
        min-width: 600px;
    }

    .pagination {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .pagination-controls {
        justify-content: center;
    }

    .view-toggle {
        width: 100%;
    }

    .toggle-btn {
        flex: 1;
    }

    .settings-actions {
        flex-direction: column;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .setting-label {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
}
