<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartBiz Admin Dashboard</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="app-container" id="app">
        <!-- Sidebar Navigation -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <span class="logo-text">SmartBiz</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item active">
                        <a href="#dashboard" class="nav-link" data-page="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#users" class="nav-link" data-page="users">
                            <i class="fas fa-users"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#reports" class="nav-link" data-page="reports">
                            <i class="fas fa-chart-bar"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#settings" class="nav-link" data-page="settings">
                            <i class="fas fa-cog"></i>
                            <span class="nav-text">Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Top Navigation Bar -->
            <header class="topbar">
                <div class="topbar-left">
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title" id="pageTitle">Dashboard</h1>
                </div>
                
                <div class="topbar-center">
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search..." id="globalSearch">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                </div>
                
                <div class="topbar-right">
                    <button class="theme-toggle" id="themeToggle" title="Toggle Dark/Light Mode">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="notification-btn" id="notificationBtn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <div class="user-profile">
                        <img src="https://via.placeholder.com/32x32/4f46e5/ffffff?text=JD" alt="User Avatar" class="user-avatar">
                        <span class="user-name">John Doe</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                </div>
            </header>

            <!-- Page Content Container -->
            <div class="page-content" id="pageContent">
                <!-- Dashboard Page -->
                <div class="page dashboard-page active" id="dashboardPage">
                    <div class="dashboard-grid">
                        <!-- Summary Cards -->
                        <div class="summary-cards">
                            <div class="summary-card">
                                <div class="card-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="card-content">
                                    <h3 class="card-title">Total Sales</h3>
                                    <p class="card-value">$124,563</p>
                                    <span class="card-change positive">+12.5%</span>
                                </div>
                            </div>
                            
                            <div class="summary-card">
                                <div class="card-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="card-content">
                                    <h3 class="card-title">Total Users</h3>
                                    <p class="card-value">2,847</p>
                                    <span class="card-change positive">+8.2%</span>
                                </div>
                            </div>
                            
                            <div class="summary-card">
                                <div class="card-icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="card-content">
                                    <h3 class="card-title">Orders</h3>
                                    <p class="card-value">1,234</p>
                                    <span class="card-change negative">-3.1%</span>
                                </div>
                            </div>
                            
                            <div class="summary-card">
                                <div class="card-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="card-content">
                                    <h3 class="card-title">Growth</h3>
                                    <p class="card-value">15.8%</p>
                                    <span class="card-change positive">+2.4%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Charts Section -->
                        <div class="charts-section">
                            <div class="chart-container">
                                <h3 class="chart-title">Monthly Revenue</h3>
                                <canvas id="revenueChart"></canvas>
                            </div>
                            
                            <div class="chart-container">
                                <h3 class="chart-title">User Distribution</h3>
                                <canvas id="userChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Page -->
                <div class="page users-page" id="usersPage">
                    <div class="page-header">
                        <h2>Users Management</h2>
                        <div class="page-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Add User
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-controls">
                        <div class="search-filter">
                            <input type="text" class="filter-input" placeholder="Search users..." id="userSearch">
                        </div>
                        <div class="filter-dropdown">
                            <select id="roleFilter" class="filter-select">
                                <option value="">All Roles</option>
                                <option value="admin">Admin</option>
                                <option value="user">User</option>
                                <option value="moderator">Moderator</option>
                            </select>
                        </div>
                        <div class="filter-dropdown">
                            <select id="statusFilter" class="filter-select">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="pending">Pending</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="table-container">
                        <table class="data-table" id="usersTable">
                            <thead>
                                <tr>
                                    <th class="sortable" data-sort="name">
                                        Name <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" data-sort="email">
                                        Email <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" data-sort="role">
                                        Role <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" data-sort="status">
                                        Status <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" data-sort="date">
                                        Join Date <i class="fas fa-sort"></i>
                                    </th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <!-- Users data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="pagination" id="usersPagination">
                        <!-- Pagination will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Reports Page -->
                <div class="page reports-page" id="reportsPage">
                    <div class="page-header">
                        <h2>Reports & Analytics</h2>
                        <div class="report-controls">
                            <div class="view-toggle">
                                <button class="toggle-btn active" data-view="daily">Daily</button>
                                <button class="toggle-btn" data-view="weekly">Weekly</button>
                                <button class="toggle-btn" data-view="monthly">Monthly</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="reports-grid">
                        <div class="report-chart">
                            <h3 class="chart-title">Visits Overview</h3>
                            <canvas id="visitsChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Settings Page -->
                <div class="page settings-page" id="settingsPage">
                    <div class="page-header">
                        <h2>Settings</h2>
                    </div>
                    
                    <div class="settings-grid">
                        <div class="settings-section">
                            <h3>General Settings</h3>
                            <div class="setting-item">
                                <label class="setting-label">
                                    <span>Enable Notifications</span>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="notificationsToggle" checked>
                                        <span class="toggle-slider"></span>
                                    </div>
                                </label>
                            </div>
                            
                            <div class="setting-item">
                                <label class="setting-label">
                                    <span>Auto-save Changes</span>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="autosaveToggle">
                                        <span class="toggle-slider"></span>
                                    </div>
                                </label>
                            </div>
                            
                            <div class="setting-item">
                                <label class="setting-label">Language</label>
                                <select class="setting-select">
                                    <option value="en">English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                    <option value="de">German</option>
                                </select>
                            </div>
                            
                            <div class="setting-item">
                                <label class="setting-label">Time Zone</label>
                                <select class="setting-select">
                                    <option value="utc">UTC</option>
                                    <option value="est">Eastern Time</option>
                                    <option value="pst">Pacific Time</option>
                                    <option value="cet">Central European Time</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="settings-section">
                            <h3>Privacy Settings</h3>
                            <div class="setting-item">
                                <label class="setting-label">
                                    <span>Make Profile Public</span>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="publicProfileToggle">
                                        <span class="toggle-slider"></span>
                                    </div>
                                </label>
                            </div>
                            
                            <div class="setting-item">
                                <label class="setting-label">
                                    <span>Allow Data Collection</span>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="dataCollectionToggle" checked>
                                        <span class="toggle-slider"></span>
                                    </div>
                                </label>
                            </div>
                        </div>
                        
                        <div class="settings-actions">
                            <button class="btn btn-secondary">Reset to Defaults</button>
                            <button class="btn btn-primary">Save Changes</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Overlay for mobile menu -->
    <div class="overlay" id="overlay"></div>

    <script src="js/main.js"></script>
</body>
</html>
