# SmartBiz Admin Dashboard

A modern, responsive admin dashboard built with vanilla HTML, CSS, and JavaScript. This project demonstrates clean UI design, interactive data visualization, and responsive web development without any frameworks.

## 🚀 Features

### ✅ Core Components
- **Responsive Sidebar Navigation** - Collapsible on desktop, slide-out on mobile
- **Sticky Top Navigation** - Search bar, notifications, user profile, and theme toggle
- **Dark/Light Mode** - Theme switching with localStorage persistence
- **Mobile-First Design** - Fully responsive across all device sizes

### 📊 Dashboard Page
- **Summary Cards** - Total Sales, Users, Orders, and Growth metrics
- **Interactive Charts** - Monthly revenue line chart and user distribution pie chart
- **Real-time Data Visualization** - Using Chart.js library

### 👥 Users Management
- **Data Table** - Sortable columns with visual indicators
- **Advanced Filtering** - Search by name/email, filter by role and status
- **Pagination** - Navigate through large datasets efficiently
- **Status Badges** - Visual indicators for user roles and status

### 📈 Reports & Analytics
- **Dynamic Charts** - Toggle between Daily, Weekly, and Monthly views
- **Interactive Data** - Bar charts showing visit statistics
- **Responsive Design** - Charts adapt to screen size

### ⚙️ Settings Page
- **Toggle Switches** - Modern UI controls for boolean settings
- **Dropdown Selectors** - Language and timezone preferences
- **Form Validation** - Save and reset functionality
- **Local Storage** - Settings persistence

## 🛠️ Technologies Used

- **HTML5** - Semantic markup structure
- **CSS3** - Modern styling with CSS Grid, Flexbox, and Custom Properties
- **Vanilla JavaScript** - ES6+ features, DOM manipulation, and event handling
- **Chart.js** - Data visualization library
- **Font Awesome** - Icon library
- **Google Fonts** - Typography (Inter font family)

## 📁 Project Structure

```
admin-dashboard/
├── index.html          # Main HTML file
├── styles/
│   └── main.css        # All CSS styles
├── js/
│   └── main.js         # JavaScript functionality
└── README.md           # Project documentation
```

## 🎨 Design Features

### Color Scheme
- **Primary**: #4f46e5 (Indigo)
- **Success**: #10b981 (Emerald)
- **Warning**: #f59e0b (Amber)
- **Danger**: #ef4444 (Red)
- **Neutral**: Various shades of gray

### Typography
- **Font Family**: Inter (Google Fonts)
- **Responsive Text Sizing**
- **Proper Contrast Ratios**

### Layout
- **CSS Grid** for complex layouts
- **Flexbox** for component alignment
- **Mobile-first responsive design**
- **Smooth transitions and animations**

## 🚀 Getting Started

1. **Clone or Download** the project files
2. **Open** `index.html` in your web browser
3. **Explore** the different pages using the sidebar navigation

No build process or server required - it's a pure client-side application!

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🎯 Key Interactions

### Navigation
- Click sidebar items to switch between pages
- Use hamburger menu on mobile devices
- Sidebar collapses automatically on smaller screens

### Theme Switching
- Click the moon/sun icon in the top bar
- Theme preference is saved to localStorage
- Smooth transitions between light and dark modes

### Data Management
- **Search**: Real-time filtering as you type
- **Sort**: Click column headers to sort data
- **Filter**: Use dropdown menus to filter by category
- **Paginate**: Navigate through data with pagination controls

### Charts
- **Dashboard Charts**: Auto-load with sample data
- **Reports Charts**: Toggle between time periods
- **Responsive**: Charts resize with screen size

## 🔧 Customization

### Adding New Data
- Modify the `generateUsersData()` function in `main.js`
- Update chart data in the `initializeCharts()` methods
- Add new summary cards by editing the HTML structure

### Styling Changes
- CSS custom properties in `:root` for easy theme customization
- Modular CSS structure for easy maintenance
- Responsive utilities for consistent spacing

### New Features
- Add new pages by creating HTML sections and updating navigation
- Extend the `AdminDashboard` class with new methods
- Use the existing CSS classes for consistent styling

## 🌟 Best Practices Implemented

- **Semantic HTML** for accessibility
- **CSS Custom Properties** for maintainable theming
- **Mobile-first responsive design**
- **Progressive enhancement**
- **Clean, readable code structure**
- **Performance optimized** (minimal dependencies)

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Feel free to fork this project and submit pull requests for any improvements!

---

**Built with ❤️ using vanilla web technologies**
